#!/usr/bin/env python3
"""
测试问题生成器使用示例

这个脚本演示了如何使用QuestionGenerator类生成各种类型的测试问题。
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.question_generator import QuestionGenerator


def demo_basic_usage():
    """演示基本使用方法"""
    print("=== 基本使用演示 ===\n")
    
    # 初始化生成器
    generator = QuestionGenerator()
    
    # 生成不同类型的问题
    print("1. 无时间查询问题:")
    no_time_questions = generator.generate_no_time_query(3)
    for i, question in enumerate(no_time_questions, 1):
        print(f"   {i}. {question}")
    
    print("\n2. 时间范围查询问题:")
    time_range_questions = generator.generate_time_range_query(3)
    for i, question in enumerate(time_range_questions, 1):
        print(f"   {i}. {question}")
    
    print("\n3. 维度对比查询问题:")
    comparison_questions = generator.generate_dimension_comparison_query(3)
    for i, question in enumerate(comparison_questions, 1):
        print(f"   {i}. {question}")
    
    print("\n4. 趋势查询问题:")
    trend_questions = generator.generate_trend_time_range_query(3)
    for i, question in enumerate(trend_questions, 1):
        print(f"   {i}. {question}")


def demo_all_types():
    """演示生成所有类型的问题"""
    print("\n=== 所有问题类型演示 ===\n")
    
    generator = QuestionGenerator()
    all_questions = generator.generate_all_question_types(count_per_type=2)
    
    for question_type, questions in all_questions.items():
        print(f"【{question_type}】")
        for i, question in enumerate(questions, 1):
            print(f"  {i}. {question}")
        print()


def demo_file_output():
    """演示输出到文件"""
    print("=== 文件输出演示 ===\n")
    
    generator = QuestionGenerator()
    output_file = project_root / "examples" / "sample_questions.md"
    
    # 生成问题到文件
    generator.generate_questions_to_file(str(output_file), count_per_type=3)
    print(f"问题已生成到文件: {output_file}")
    
    # 读取并显示文件内容的前几行
    if output_file.exists():
        print("\n文件内容预览:")
        with open(output_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            for i, line in enumerate(lines[:20]):  # 显示前20行
                print(f"  {line.rstrip()}")
            if len(lines) > 20:
                print(f"  ... (还有 {len(lines) - 20} 行)")


def demo_flat_list():
    """演示获取扁平化问题列表"""
    print("\n=== 扁平化列表演示 ===\n")
    
    generator = QuestionGenerator()
    flat_questions = generator.get_flat_question_list(count_per_type=1)
    
    print(f"共生成 {len(flat_questions)} 个问题:")
    for i, question in enumerate(flat_questions, 1):
        print(f"  {i:2d}. {question}")


def demo_custom_generation():
    """演示自定义生成逻辑"""
    print("\n=== 自定义生成演示 ===\n")
    
    generator = QuestionGenerator()
    
    # 生成特定指标的问题
    print("1. 专注于审核量指标的问题:")
    audit_questions = []
    for _ in range(5):
        questions = generator.generate_no_time_query(1)
        for question in questions:
            if "审核量" in question:
                audit_questions.append(question)
                break
    
    for i, question in enumerate(audit_questions, 1):
        print(f"   {i}. {question}")
    
    # 生成特定维度的问题
    print("\n2. 专注于群组维度的问题:")
    group_questions = []
    for _ in range(5):
        questions = generator.generate_no_time_query(1)
        for question in questions:
            if "群组" in question:
                group_questions.append(question)
                break
    
    for i, question in enumerate(group_questions, 1):
        print(f"   {i}. {question}")


def main():
    """主函数"""
    print("测试问题生成器使用示例\n")
    print("=" * 50)
    
    try:
        # 基本使用演示
        demo_basic_usage()
        
        # 所有类型演示
        demo_all_types()
        
        # 文件输出演示
        demo_file_output()
        
        # 扁平化列表演示
        demo_flat_list()
        
        # 自定义生成演示
        demo_custom_generation()
        
        print("\n" + "=" * 50)
        print("演示完成！")
        print("\n使用提示:")
        print("1. 可以修改 config/renli_table.yaml 来自定义维度和指标")
        print("2. 可以在 QuestionGenerator 类中添加新的问题模板")
        print("3. 使用 python main.py --mode generate 来生成问题")
        print("4. 使用 python main.py --mode run-generated 来运行生成的问题测试")
        
    except Exception as e:
        print(f"演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
