import asyncio
import argparse
from utils.config_loader import config
from core.test_runner import TestRunner
from core.question_generator import QuestionGenerator
from core.llm_filter import LLMQuestionFilter
from core.auto_tester import AutoTester  # 导入新的自动测试模块


async def run_tests():
    """运行自动化测试流程（从飞书表格读取问题）"""
    # 初始化测试执行器
    test_runner = TestRunner(config)

    # 运行测试
    await test_runner.run_tests()


async def run_generated_tests(count_per_type: int = 3):
    """运行自动生成的测试问题"""
    # 初始化测试执行器
    test_runner = TestRunner(config)

    # 运行生成的问题测试
    await test_runner.run_generated_questions(count_per_type)


def generate_questions(count_per_type: int = 5, output_file: str = None, use_llm_filter: bool = False, filter_prompt: str = None):
    """生成测试问题
    
    Args:
        count_per_type: 每种类型生成的问题数量
        output_file: 输出文件路径，如果为None则打印到控制台
        use_llm_filter: 是否使用大模型筛查优化问题
        filter_prompt: 大模型筛查的提示词，为None时使用默认提示词
    """
    print("正在生成测试问题...")

    # 初始化问题生成器
    generator = QuestionGenerator()

    # 生成问题
    all_questions = generator.generate_all_question_types(count_per_type)
    
    # 使用大模型筛查优化问题
    if use_llm_filter:
        print("正在使用大模型筛查优化问题...")
        question_filter = LLMQuestionFilter(config)
        all_questions = question_filter.filter_questions(all_questions, filter_prompt)
        print("问题筛查优化完成")

    if output_file:
        # 生成问题到文件
        if use_llm_filter:
            generator.save_questions_to_file(all_questions, output_file)
        else:
            generator.generate_questions_to_file(output_file, count_per_type)
        print(f"问题已生成并保存到: {output_file}")
    else:
        # 生成问题并打印到控制台
        print("\n=== 自动生成的测试问题 ===\n")

        for question_type, questions in all_questions.items():
            print(f"## {question_type}")
            for i, question in enumerate(questions, 1):
                print(f"  {i}. {question}")
            print()


def insert_questions_to_feishu(count_per_type: int = 3, start_row: int = None, use_llm_filter: bool = False, filter_prompt: str = None):
    """生成问题并插入到飞书表格
    
    Args:
        count_per_type: 每种类型生成的问题数量
        start_row: 插入飞书表格的起始行号
        use_llm_filter: 是否使用大模型筛查优化问题
        filter_prompt: 大模型筛查的提示词，为None时使用默认提示词
    """
    print("正在生成问题并插入到飞书表格...")

    try:
        # 初始化问题生成器和飞书管理器
        generator = QuestionGenerator()
        feishu_manager = TestRunner(config).feishu_manager

        # 生成问题
        all_questions = generator.generate_all_question_types(count_per_type)
        
        # 使用大模型筛查优化问题
        if use_llm_filter:
            print("正在使用大模型筛查优化问题...")
            question_filter = LLMQuestionFilter(config)
            all_questions = question_filter.filter_questions(all_questions, filter_prompt)
            print("问题筛查优化完成")

        print(f"已生成 {sum(len(questions) for questions in all_questions.values())} 个问题")

        # 插入到飞书表格
        result = feishu_manager.insert_generated_questions_by_type(
            questions_by_type=all_questions,
            start_row=start_row
        )

        if result['success']:
            print(f"\n✅ 成功插入 {result['total_inserted']} 个问题到飞书表格")
            print(f"📍 插入位置: 第 {result['start_row']} 行到第 {result['end_row']} 行")
            print(f"📊 列位置: {result['target_col']} 列")

            if result['total_failed'] > 0:
                print(f"⚠️  失败 {result['total_failed']} 个问题")
        else:
            print("❌ 插入失败")

    except Exception as e:
        print(f"❌ 插入过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()


def generate_and_test_questions(count_per_type: int = 3, start_row: int = None, use_llm_filter: bool = False, filter_prompt: str = None):
    """生成问题、插入到飞书表格并自动执行测试
    
    Args:
        count_per_type: 每种类型生成的问题数量
        start_row: 插入飞书表格的起始行号
        use_llm_filter: 是否使用大模型筛查优化问题
        filter_prompt: 大模型筛查的提示词，为None时使用默认提示词
    """
    print("正在生成问题并插入到飞书表格...")

    try:
        # 初始化问题生成器和飞书管理器
        generator = QuestionGenerator()
        feishu_manager = TestRunner(config).feishu_manager

        # 生成问题
        all_questions = generator.generate_all_question_types(count_per_type)
        
        # 使用大模型筛查优化问题
        if use_llm_filter:
            print("正在使用大模型筛查优化问题...")
            question_filter = LLMQuestionFilter(config)
            all_questions = question_filter.filter_questions(all_questions, filter_prompt)
            print("问题筛查优化完成")

        print(f"已生成 {sum(len(questions) for questions in all_questions.values())} 个问题")

        # 插入到飞书表格
        result = feishu_manager.insert_generated_questions_by_type(
            questions_by_type=all_questions,
            start_row=start_row
        )

        if result['success']:
            print(f"\n✅ 成功插入 {result['total_inserted']} 个问题到飞书表格")
            print(f"📍 插入位置: 第 {result['start_row']} 行到第 {result['end_row']} 行")
            print(f"📊 列位置: {result['target_col']} 列")

            if result['total_failed'] > 0:
                print(f"⚠️  失败 {result['total_failed']} 个问题")
                
            # 准备问题和行号列表
            questions_with_rows = []
            current_row = result['start_row']
            
            for question_type, questions in all_questions.items():
                for question in questions:
                    questions_with_rows.append((current_row, question))
                    current_row += 1
            
            # 执行自动化测试
            print("\n开始执行自动化测试...")
            auto_tester = AutoTester(config)
            auto_tester.run_tests_for_questions(questions_with_rows)
            
        else:
            print("❌ 插入失败")

    except Exception as e:
        print(f"❌ 处理过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()


def show_question_examples():
    """显示问题生成示例"""
    generator = QuestionGenerator()

    print("\n=== 问题生成示例 ===\n")

    examples = {
        "无时间查询": generator.generate_no_time_query(2),
        "单时间点查询": generator.generate_single_time_query(2),
        "时间范围查询": generator.generate_time_range_query(2),
        "维度对比查询": generator.generate_dimension_comparison_query(2),
        "趋势查询": generator.generate_trend_time_range_query(2),
        "下钻查询": generator.generate_single_drill_down_query(2)
    }

    for example_type, questions in examples.items():
        print(f"### {example_type}")
        for question in questions:
            print(f"  - {question}")
        print()


async def main():
    """主函数，根据命令行参数执行不同功能"""
    parser = argparse.ArgumentParser(description='CBAO自动化测试工具')
    parser.add_argument('--mode', choices=['test', 'generate', 'run-generated', 'insert-feishu', 'example', 'auto-test'],
                       default='test', help='运行模式：test(从飞书读取并运行测试), generate(生成问题), run-generated(运行生成的问题测试), insert-feishu(生成问题并插入飞书表格), example(显示示例), auto-test(生成问题并自动测试)')
    parser.add_argument('--count', type=int, default=5,
                       help='每种问题类型生成的数量 (默认: 5)')
    parser.add_argument('--output', type=str,
                       help='问题输出文件路径 (仅在generate模式下有效)')
    parser.add_argument('--start-row', type=int,
                       help='插入飞书表格的起始行号 (仅在insert-feishu和auto-test模式下有效)')
    parser.add_argument('--use-llm-filter', action='store_true',
                       help='使用大模型筛查优化生成的问题')
    parser.add_argument('--filter-prompt', type=str,
                       help='大模型筛查的自定义提示词')

    args = parser.parse_args()

    if args.mode == 'test':
        await run_tests()
    elif args.mode == 'generate':
        generate_questions(args.count, args.output, args.use_llm_filter, args.filter_prompt)
    elif args.mode == 'run-generated':
        await run_generated_tests(args.count)
    elif args.mode == 'insert-feishu':
        insert_questions_to_feishu(args.count, args.start_row, args.use_llm_filter, args.filter_prompt)
    elif args.mode == 'example':
        show_question_examples()
    elif args.mode == 'auto-test':
        generate_and_test_questions(args.count, args.start_row, args.use_llm_filter, args.filter_prompt)


if __name__ == "__main__":
    asyncio.run(main())

