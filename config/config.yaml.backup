llm:
  api_key: 49d2f71d-ae01-407f-9094-ae1a41442cb0
  base_url: https://ark.cn-beijing.volces.com/api/v3
  model_name: ep-20250506195244-6ph27
feishu:
  app_id: cli_a66eb807d470900e
  app_secret: YCJNhQyIqRnOhlKgDzJJ4uJvoJQqvf71
  sheet_id: ghyqFY
  spreadsheet_token: QEGns4FYKhCWQHt4qVvc70Qvndh_ghyqFY
  feishu_token: QEGns4FYKhCWQHt4qVvc70Qvndh_ghyqFY
  user_access_token: t-g1047aefNRJT5N62HSV2JNXCTR7R4U3TJANHA7EC

  input_row: 2
  input_col: D
  output_row: null
  output_col: D
  pic_col: F
  # 新增列配置
  question_type_col: "A"  # 问题类型列（业务指标或人力指标）
  test_type_col: "B"      # 测试类型列
  subtype_col: "C"        # 子类型列
  
  max_concurrent_threads: 3  # 自动测试的最大并发线程数
  headless_browser: false    # 是否使用无头浏览器模式
  screenshot_dir: screenshots
  
  # 自动测试相关配置
  vision_detection:
    max_wait_time: 60        # 等待回答的最大时间（秒）
    check_interval: 5        # 检查间隔（秒）
    target_text: "重新生成"   # 表示回答完成的目标文本
    max_retries: 3           # 最大重试次数
  
  # 截图边界配置
  screenshot_boundaries:
    left: 200                # 左边界（像素）
    top: 80                  # 上边界（像素）
    right: 1200              # 右边界（像素）
    bottom: 800              # 下边界（像素）
  
  smart_screenshot:
    enabled: true
    strategy: element_first
    element_selectors:
    - '[class*="conversation"]'
    - '[class*="chat"]'
    - '[class*="dialog"]'
    - '[class*="message"]'
    - '[class*="answer"]'
    - '[class*="response"]'
    - main
    - '[role="main"]'
    - .main-content
    - '#main-content'
    center_area:
      left_margin: 200
      top_margin: 80
      right_margin: 50
      bottom_margin: 50
    min_element_size:
      width: 300
      height: 200
tars_agent:
  url: http://localhost:port/mcp