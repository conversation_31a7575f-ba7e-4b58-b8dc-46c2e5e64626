# CBAO 自动化测试工具

## 项目简介
<<<<<<< HEAD
CBAO自动化测试工具是一个用于自动化测试的工具集，旨在提高测试效率和测试覆盖率。该工具支持接口测试、UI测试等多种测试场景。
=======
CBAO自动化测试工具是一个用于自动化测试的工具集，旨在提高测试效率和测试覆盖率。该工具支持接口测试、UI测试，并新增了智能测试问题生成功能。
>>>>>>> acb375da999c981548e9853f5ab3cc4b5df0585d

## 功能特点
- 支持接口自动化测试
- 支持UI自动化测试
<<<<<<< HEAD
- 提供测试报告生成功能
- 支持多环境配置
- 可扩展的测试框架
=======
- **🆕 智能测试问题生成器** - 根据维度和指标自动生成测试问题
- **🆕 大模型问题筛查优化** - 使用大模型优化生成的问题质量
- **🆕 全自动测试流程** - 生成问题、插入表格、自动执行测试并截图
- 提供测试报告生成功能
- 支持多环境配置
- 可扩展的测试框架
- 支持飞书表格集成

## 新功能：测试问题生成器

### 支持的问题类型
- **指标达成类型**（8种）：无时间查询、单时间点查询、时间范围查询、时间对比查询、维度对比查询、维度计算聚合、时间计算聚合、多指标查询
- **趋势查询类型**（2种）：时间范围查询、时间对比查询
- **下钻查询类型**（2种）：单层下钻、多层下钻

### 快速开始

#### 1. 查看问题生成示例
```bash
python main.py --mode example
```

#### 2. 生成测试问题
```bash
# 生成问题到控制台
python main.py --mode generate --count 5

# 生成问题到文件
python main.py --mode generate --count 10 --output my_questions.md

# 🆕 使用大模型筛查优化问题
python main.py --mode generate --count 5 --use-llm-filter

# 🆕 使用自定义提示词筛查优化问题
python main.py --mode generate --count 5 --use-llm-filter --filter-prompt "请优化这些问题，使其更专业"
```

#### 3. 运行生成的问题测试
```bash
python main.py --mode run-generated --count 3
```

#### 4. 生成问题并插入到飞书表格
```bash
# 自动查找空行插入
python main.py --mode insert-feishu --count 3

# 指定起始行号插入
python main.py --mode insert-feishu --count 5 --start-row 100

# 🆕 使用大模型筛查优化后插入
python main.py --mode insert-feishu --count 3 --use-llm-filter
```

#### 5. 运行原有的飞书表格测试
```bash
python main.py --mode test
```

#### 6. 🆕 全自动测试流程
```bash
# 生成问题、插入表格并自动执行测试
python main.py --mode auto-test --count 3

# 使用大模型筛查优化问题后自动测试
python main.py --mode auto-test --count 3 --use-llm-filter

# 指定起始行号
python main.py --mode auto-test --count 3 --start-row 100
```

### 编程接口使用
```python
from core.question_generator import QuestionGenerator
from core.llm_filter import LLMQuestionFilter
from core.auto_tester import AutoTester
from utils.config_loader import config

# 初始化生成器
generator = QuestionGenerator()

# 生成特定类型问题
questions = generator.generate_no_time_query(5)

# 生成所有类型问题
all_questions = generator.generate_all_question_types(count_per_type=3)

# 🆕 使用大模型筛查优化问题
question_filter = LLMQuestionFilter(config)
optimized_questions = question_filter.filter_questions(all_questions)

# 🆕 自动执行测试
auto_tester = AutoTester(config)
questions_with_rows = [(1, "问题1"), (2, "问题2"), (3, "问题3")]
auto_tester.run_tests_for_questions(questions_with_rows)
```

## 新功能：全自动测试流程

### 功能说明
全自动测试流程将问题生成、表格插入和自动化测试集成在一起，实现一键式测试：

1. 自动生成多种类型的测试问题
2. 可选使用大模型筛查优化问题质量
3. 自动将问题插入到飞书表格
4. 自动启动浏览器执行测试
5. 自动截取回答结果并上传到飞书表格
6. 支持多线程并行测试，提高效率

### 使用方法

#### 命令行使用
```bash
# 基本使用
python main.py --mode auto-test --count 3

# 使用大模型筛查优化问题
python main.py --mode auto-test --count 3 --use-llm-filter

# 指定起始行号
python main.py --mode auto-test --count 3 --start-row 100
```

#### 编程接口使用
```python
from core.question_generator import QuestionGenerator
from core.llm_filter import LLMQuestionFilter
from core.auto_tester import AutoTester
from utils.config_loader import config

# 生成问题
generator = QuestionGenerator()
all_questions = generator.generate_all_question_types(count_per_type=2)

# 准备问题和行号列表
questions_with_rows = []
current_row # 起始行号
for question_type, questions in all_questions.items():
    for question in questions:
        questions_with_rows.append((current_row, question))
        current_row += 1

# 执行自动化测试
auto_tester = AutoTester(config)
auto_tester.run_tests_for_questions(questions_with_rows)
```

### 配置说明
自动测试功能使用 `config/config.yaml` 中的配置：

```yaml
feishu:
  # 其他配置...
  max_concurrent_threads: 3  # 最大并发线程数
  headless_browser: false    # 是否使用无头浏览器
  
  # 自动测试相关配置
  vision_detection:
    max_wait_time: 60        # 等待回答的最大时间（秒）
    check_interval: 5        # 检查间隔（秒）
    target_text: "重新生成"   # 表示回答完成的目标文本
    max_retries: 3           # 最大重试次数
  
  # 截图边界配置
  screenshot_boundaries:
    left: 200                # 左边界（像素）
    top: 80                  # 上边界（像素）
    right: 1200              # 右边界（像素）
    bottom: 800              # 下边界（像素）
```

## 新功能：大模型问题筛查优化

### 功能说明
该功能使用大模型对自动生成的问题进行筛查和优化，确保问题：
- 语句通顺、表达清晰
- 符合中文语法习惯
- 专业性强，适合数据分析场景
- 保持问题的原始意图和类型不变

### 使用方法

#### 命令行使用
```bash
# 基本使用
python main.py --mode generate --count 5 --use-llm-filter

# 自定义提示词
python main.py --mode generate --count 5 --use-llm-filter --filter-prompt "请优化以下问题，使其更加专业和清晰"

# 生成到文件
python main.py --mode generate --count 5 --use-llm-filter --output optimized_questions.md

# 插入到飞书表格
python main.py --mode insert-feishu --count 3 --use-llm-filter
```

#### 编程接口使用
```python
from core.question_generator import QuestionGenerator
from core.llm_filter import LLMQuestionFilter
from utils.config_loader import config

# 初始化组件
generator = QuestionGenerator()
question_filter = LLMQuestionFilter(config)

# 生成问题
all_questions = generator.generate_all_question_types(count_per_type=3)

# 使用默认提示词筛查优化
optimized_questions = question_filter.filter_questions(all_questions)

# 使用自定义提示词
custom_prompt = """
请对以下问题进行优化，使其更加专业、清晰，并符合数据分析领域的表达习惯。
保持问题的原始意图不变，但可以调整表达方式使其更加准确。
"""
optimized_questions = question_filter.filter_questions(all_questions, custom_prompt)
```

### 配置说明
大模型筛查功能使用 `config/config.yaml` 中的 `llm` 配置：

```yaml
llm:
  api_key: your_api_key
  base_url: https://api.example.com
  model_name: model_name
```
>>>>>>> acb375da999c981548e9853f5ab3cc4b5df0585d

## 安装说明
1. 确保已安装Python 3.7+
2. 克隆项目到本地
<<<<<<< HEAD
=======
```bash
git clone https://bits.bytedance.net/code/zhangziqiang.ethan/cbao_auto_testing_tools.git
cd cbao_auto_testing_tools
```
3. 安装依赖
```bash
pip install -r requirements.txt
```

## 配置说明
- `config/config.yaml` - 主要配置文件
- `config/renli_table.yaml` - 维度和指标配置（用于问题生成）

## 使用示例
查看 `examples/question_generator_example.py` 了解详细使用方法。

## 文档
- [测试问题生成器使用指南](docs/question_generator_guide.md)

## Git操作指南

### 下载项目
```bash
# 克隆仓库
git clone https://bits.bytedance.net/code/zhangziqiang.ethan/cbao_auto_testing_tools.git
cd cbao_auto_testing_tools
```

### 上传更改
```bash
# 查看状态
git status

# 添加文件
git add .

# 提交更改
git commit -m "描述你的更改"

# 推送到远程仓库
git push origin master
```

### 日常开发
```bash
# 拉取最新代码
git pull origin master

# 创建新分支
git checkout -b feature-branch

# 查看提交历史
git log --oneline
```
>>>>>>> acb375da999c981548e9853f5ab3cc4b5df0585d
