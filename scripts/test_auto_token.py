#!/usr/bin/env python3
"""
测试自动token管理功能

这个脚本测试FeishuManager的自动token获取和更新功能。
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.feishu_manager import FeishuManager
from core.question_generator import QuestionGenerator
from utils.config_loader import config


def test_token_management():
    """测试token管理功能"""
    print("=== 测试自动Token管理功能 ===\n")
    
    try:
        print("1. 初始化FeishuManager（会自动检查和更新token）...")
        feishu_manager = FeishuManager(config)
        print("✅ FeishuManager初始化成功")
        
        print(f"\n2. 当前使用的token: {feishu_manager.user_access_token[:20]}...")
        
        print(f"\n3. 验证token有效性...")
        if feishu_manager._validate_token():
            print("✅ Token验证成功")
        else:
            print("❌ Token验证失败")
            return False
        
        print(f"\n4. 测试确保有效token功能...")
        if feishu_manager.ensure_valid_token():
            print("✅ 确保有效token成功")
        else:
            print("❌ 确保有效token失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_insert_with_auto_token():
    """测试使用自动token管理的插入功能"""
    print("\n=== 测试自动Token插入功能 ===\n")
    
    try:
        print("1. 初始化组件...")
        feishu_manager = FeishuManager(config)
        generator = QuestionGenerator()
        
        print("2. 生成测试问题...")
        test_questions = [
            "测试问题1：自动token管理测试",
            "测试问题2：动态获取用户访问令牌测试",
            "测试问题3：配置文件自动更新测试"
        ]
        
        print(f"生成了 {len(test_questions)} 个测试问题")
        for i, question in enumerate(test_questions, 1):
            print(f"  {i}. {question}")
        
        print(f"\n3. 插入问题到飞书表格（会自动管理token）...")
        result = feishu_manager.insert_questions_to_sheet(
            questions=test_questions,
            start_row=None,  # 自动查找空行
            target_col='D'   # 插入到D列
        )
        
        print(f"\n4. 插入结果:")
        if result['success']:
            print(f"✅ 成功插入 {result['inserted_count']}/{result['total_count']} 个问题")
            print(f"📍 插入位置: 第 {result['start_row']} 行到第 {result['end_row']} 行")
            print(f"📊 目标列: {result['target_col']}")
            
            if result['failed_count'] > 0:
                print(f"⚠️  失败 {result['failed_count']} 个问题")
        else:
            print("❌ 插入失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_generated_questions_with_auto_token():
    """测试生成问题并使用自动token管理插入"""
    print("\n=== 测试生成问题+自动Token插入 ===\n")
    
    try:
        print("1. 初始化组件...")
        feishu_manager = FeishuManager(config)
        generator = QuestionGenerator()
        
        print("2. 生成分类问题...")
        questions_by_type = generator.generate_all_question_types(count_per_type=1)
        
        total_questions = sum(len(questions) for questions in questions_by_type.values())
        print(f"生成了 {len(questions_by_type)} 个类型，共 {total_questions} 个问题")
        
        print(f"\n3. 按类型插入到飞书表格...")
        result = feishu_manager.insert_generated_questions_by_type(
            questions_by_type=questions_by_type,
            start_row=None
        )
        
        print(f"\n4. 插入结果:")
        if result['success']:
            print(f"✅ 总共成功插入 {result['total_inserted']} 个问题")
            print(f"📍 插入位置: 第 {result['start_row']} 行到第 {result['end_row']} 行")
            
            if result['total_failed'] > 0:
                print(f"⚠️  失败 {result['total_failed']} 个问题")
        else:
            print("❌ 插入失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("自动Token管理功能测试")
    print("=" * 50)
    
    # 检查配置
    feishu_config = config.get('feishu', {})
    required_keys = ['app_id', 'app_secret', 'spreadsheet_token']
    
    missing_keys = [key for key in required_keys if not feishu_config.get(key)]
    if missing_keys:
        print(f"❌ 缺少必要的飞书配置: {', '.join(missing_keys)}")
        print("请检查 config/config.yaml 文件中的飞书配置")
        return
    
    print("✅ 飞书配置检查通过")
    
    try:
        # 运行测试
        success_count = 0
        total_tests = 3
        
        if test_token_management():
            success_count += 1
        
        if test_insert_with_auto_token():
            success_count += 1
        
        if test_generated_questions_with_auto_token():
            success_count += 1
        
        print("\n" + "=" * 50)
        print(f"🎯 测试完成: {success_count}/{total_tests} 个测试通过")
        
        if success_count == total_tests:
            print("🎉 所有测试通过！自动token管理功能正常工作")
            print("\n💡 现在你可以:")
            print("1. 直接使用 python main.py --mode insert-feishu 插入问题")
            print("2. 系统会自动管理和更新用户访问令牌")
            print("3. 无需手动更新config.yaml中的token")
        else:
            print("⚠️  部分测试失败，请检查配置和网络连接")
        
    except KeyboardInterrupt:
        print("\n\n⏹️  测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中出现未预期的错误: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
