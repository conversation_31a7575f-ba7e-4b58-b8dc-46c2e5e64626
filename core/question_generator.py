import random
import yaml
from typing import Dict, List, Any, Tuple
from pathlib import Path
import logging
from utils.config_loader import ConfigLoader

class QuestionGenerator:
    #测试问题生成器，根据维度和指标生成不同类型的测试问题 
    def __init__(self, config_path: str = None):
        """
        初始化问题生成器
        
        Args:
            config_path (str): renli_table.yaml配置文件路径
        """
        self.logger = logging.getLogger(__name__)
        
        if config_path is None:
            # 使用默认路径
            project_root = Path(__file__).parent.parent
            config_path = project_root / 'config' / 'renli_table.yaml'
        
        self.config_path = Path(config_path)
        self.config_data = self._load_config()
        
        # 问题模板定义
        self.question_templates = {
            # 指标达成类型
            'no_time_query': [
                "{dimension}的{metric}？",
                "{dimension}的{metric}是多少？",
                "查询{dimension}的{metric}",
                "{dimension}{metric}情况如何？"
            ],
            'single_time_query': [
                "{time_point}{dimension}的{metric}？",
                "{time_point}{dimension}的{metric}是多少？",
                "查询{time_point}{dimension}的{metric}",
                "{time_point}{dimension}{metric}情况如何？"
            ],
            'time_range_query': [
                "{time_range}内{dimension}的{metric}？",
                "{time_range}{dimension}的{metric}总计是多少？",
                "统计{time_range}{dimension}的{metric}",
                "{time_range}期间{dimension}{metric}情况"
            ],
            'time_comparison_query': [
                "{dimension}的{metric}，{time1}与{time2}对比？",
                "对比{time1}和{time2}{dimension}的{metric}",
                "{dimension}{metric}在{time1}和{time2}的差异？",
                "{time1}相比{time2}，{dimension}的{metric}变化如何？"
            ],
            'dimension_comparison_query': [
                "{dimension1}与{dimension2}的{metric}对比？",
                "对比{dimension1}和{dimension2}的{metric}",
                "{dimension1}和{dimension2}哪个{metric}更高？",
                "{metric}方面，{dimension1}与{dimension2}的差异？"
            ],
            'dimension_aggregation_query': [
                "按{dimension}统计{metric}的{aggregation}？",
                "{dimension}维度下{metric}的{aggregation}是多少？",
                "计算各{dimension}的{metric}{aggregation}",
                "{dimension}分组的{metric}{aggregation}情况"
            ],
            'time_aggregation_query': [
                "按{time_unit}统计{dimension}的{metric}{aggregation}？",
                "{dimension}的{metric}按{time_unit}的{aggregation}趋势？",
                "计算{dimension}{metric}的{time_unit}{aggregation}",
                "{time_unit}维度下{dimension}{metric}的{aggregation}"
            ],
            'multi_metric_query': [
                "{dimension}的{metric1}和{metric2}？",
                "查询{dimension}的{metric1}、{metric2}情况",
                "{dimension}在{metric1}和{metric2}方面的表现？",
                "统计{dimension}的{metric1}及{metric2}"
            ],
            # 趋势查询类型
            'trend_time_range': [
                "{time_range}内{dimension}的{metric}趋势？",
                "{dimension}的{metric}在{time_range}的变化趋势？",
                "分析{time_range}{dimension}{metric}的走势",
                "{time_range}期间{dimension}{metric}趋势如何？"
            ],
            'trend_time_comparison': [
                "{dimension}的{metric}趋势，{time1}与{time2}对比？",
                "对比{time1}和{time2}{dimension}{metric}的趋势变化",
                "{dimension}{metric}在{time1}和{time2}的趋势差异？",
                "分析{dimension}{metric}从{time1}到{time2}的趋势"
            ],
            # 下钻查询类型
            'single_drill_down': [
                "{parent_dimension}下各{child_dimension}的{metric}？",
                "按{child_dimension}细分{parent_dimension}的{metric}",
                "{parent_dimension}中{child_dimension}维度的{metric}分布？",
                "下钻查看{parent_dimension}各{child_dimension}的{metric}"
            ],
            'multi_drill_down': [
                "{parent_dimension}下按{child_dimension1}和{child_dimension2}的{metric}？",
                "多层下钻：{parent_dimension}->{child_dimension1}->{child_dimension2}的{metric}",
                "{parent_dimension}按{child_dimension1}、{child_dimension2}细分的{metric}",
                "层级查询{parent_dimension}的{child_dimension1}和{child_dimension2}维度{metric}"
            ]
        }
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as file:
                return yaml.safe_load(file)
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            return {}
    
    def _get_random_item(self, items: List[str]) -> str:
        """从列表中随机选择一个项目"""
        item = random.choice(items)
        if "(" in item:
            if random.random()<0.5:
                item = item.split("(")[0]
            else:
                item = item.split("(")[1][:-1]
        return item
    
    def _get_dimension_value(self, dimension_key: str) -> str:
        """获取维度的随机值"""
        dimensions = self.config_data.get('维度', {})
        if dimension_key in dimensions:
            values = dimensions[dimension_key]
            if isinstance(values, list):
                return self._get_random_item(values)
            elif isinstance(values, str):
                return values
        return dimension_key
    
    def _get_metric_value(self, metric_key: str = None) -> str:
        """获取指标的随机值"""
        metrics = self.config_data.get('指标', {})
        print(metrics)
        if metric_key and metric_key in metrics:
            values = metrics[metric_key]
            if isinstance(values, list):
                return self._get_random_item(values)
            elif isinstance(values, str):
                return values
        else:
            # 随机选择一个指标类别
            if metrics:
                category = random.choice(list(metrics.keys()))
                values = metrics[category]
                if isinstance(values, list):
                    return self._get_random_item(values)
                elif isinstance(values, str):
                    return values
        return "审核量"
    
    def _get_time_value(self, time_type: str = "time_range") -> str:
        """获取时间相关的随机值"""
        limits = self.config_data.get('限制', {})
        time_ranges = limits.get('时间范围', [])
        
        if time_type == "time_range":
            return self._get_random_item(time_ranges) if time_ranges else "本月"
        elif time_type == "time_point":
            single_times = ["今天", "昨天", "本周", "上周", "本月", "上月"]
            return self._get_random_item(single_times)
        elif time_type == "time_unit":
            units = ["日", "周", "月", "季度"]
            return self._get_random_item(units)
        
        return self._get_random_item(time_ranges) if time_ranges else "本月"
    
    def _get_aggregation_value(self) -> str:
        """获取聚合方式的随机值"""
        limits = self.config_data.get('限制', {})
        aggregations = limits.get('聚合方式', [])
        return self._get_random_item(aggregations) if aggregations else "总和"

    def generate_no_time_query(self, count: int = 5) -> List[str]:
        """生成无时间查询问题"""
        questions = []
        templates = self.question_templates['no_time_query']

        for _ in range(count):
            template = self._get_random_item(templates)
            dimension = self._get_dimension_value(random.choice(list(self.config_data.get('维度', {}).keys())))
            metric = self._get_metric_value()

            question = template.format(dimension=dimension, metric=metric)
            questions.append(question)

        return questions

    def generate_single_time_query(self, count: int = 5) -> List[str]:
        """生成单时间点查询问题"""
        questions = []
        templates = self.question_templates['single_time_query']

        for _ in range(count):
            template = self._get_random_item(templates)
            dimension = self._get_dimension_value(random.choice(list(self.config_data.get('维度', {}).keys())))
            metric = self._get_metric_value()
            time_point = self._get_time_value("time_point")

            question = template.format(dimension=dimension, metric=metric, time_point=time_point)
            questions.append(question)

        return questions

    def generate_time_range_query(self, count: int = 5) -> List[str]:
        """生成时间范围查询问题"""
        questions = []
        templates = self.question_templates['time_range_query']

        for _ in range(count):
            template = self._get_random_item(templates)
            dimension = self._get_dimension_value(random.choice(list(self.config_data.get('维度', {}).keys())))
            metric = self._get_metric_value()
            time_range = self._get_time_value("time_range")

            question = template.format(dimension=dimension, metric=metric, time_range=time_range)
            questions.append(question)

        return questions

    def generate_time_comparison_query(self, count: int = 5) -> List[str]:
        """生成时间对比查询问题"""
        questions = []
        templates = self.question_templates['time_comparison_query']

        for _ in range(count):
            template = self._get_random_item(templates)
            dimension = self._get_dimension_value(random.choice(list(self.config_data.get('维度', {}).keys())))
            metric = self._get_metric_value()
            time1 = self._get_time_value("time_point")
            time2 = self._get_time_value("time_point")

            # 确保两个时间不同
            while time1 == time2:
                time2 = self._get_time_value("time_point")

            question = template.format(dimension=dimension, metric=metric, time1=time1, time2=time2)
            questions.append(question)

        return questions

    def generate_dimension_comparison_query(self, count: int = 5) -> List[str]:
        """生成维度对比查询问题"""
        questions = []
        templates = self.question_templates['dimension_comparison_query']

        for _ in range(count):
            template = self._get_random_item(templates)
            dimension_keys = list(self.config_data.get('维度', {}).keys())

            # 选择同一维度类型的不同值进行对比
            dimension_key = random.choice(dimension_keys)
            dimension_values = self.config_data.get('维度', {}).get(dimension_key, [])

            if isinstance(dimension_values, list) and len(dimension_values) >= 2:
                dimension1, dimension2 = random.sample(dimension_values, 2)
                if '(' in dimension1:
                    if random.random()<0.5:
                        dimension1 = dimension1.split("(")[0]
                    else:
                        dimension1 = dimension1.split("(")[1][:-1]
                if '(' in dimension2:
                    if random.random()<0.5:
                        dimension2 = dimension2.split("(")[0]
                    else:
                        dimension2 = dimension2.split("(")[1][:-1]

            else:
                # 如果同一维度值不够，选择不同维度
                dimension1 = self._get_dimension_value(dimension_key)
                other_key = random.choice([k for k in dimension_keys if k != dimension_key])
                dimension2 = self._get_dimension_value(other_key)

                

            metric = self._get_metric_value()

            question = template.format(dimension1=dimension1, dimension2=dimension2, metric=metric)
            questions.append(question)

        return questions

    def generate_dimension_aggregation_query(self, count: int = 5) -> List[str]:
        """生成维度计算聚合查询问题"""
        questions = []
        templates = self.question_templates['dimension_aggregation_query']

        for _ in range(count):
            template = self._get_random_item(templates)
            dimension = self._get_dimension_value(random.choice(list(self.config_data.get('维度', {}).keys())))
            metric = self._get_metric_value()
            aggregation = self._get_aggregation_value()

            question = template.format(dimension=dimension, metric=metric, aggregation=aggregation)
            questions.append(question)

        return questions

    def generate_time_aggregation_query(self, count: int = 5) -> List[str]:
        """生成时间计算聚合查询问题"""
        questions = []
        templates = self.question_templates['time_aggregation_query']

        for _ in range(count):
            template = self._get_random_item(templates)
            dimension = self._get_dimension_value(random.choice(list(self.config_data.get('维度', {}).keys())))
            metric = self._get_metric_value()
            aggregation = self._get_aggregation_value()
            time_unit = self._get_time_value("time_unit")

            question = template.format(dimension=dimension, metric=metric, aggregation=aggregation, time_unit=time_unit)
            questions.append(question)

        return questions

    def generate_multi_metric_query(self, count: int = 5) -> List[str]:
        """生成多指标查询问题"""
        questions = []
        templates = self.question_templates['multi_metric_query']

        for _ in range(count):
            template = self._get_random_item(templates)
            dimension = self._get_dimension_value(random.choice(list(self.config_data.get('维度', {}).keys())))

            # 选择两个不同的指标
            all_metrics = []
            for category_metrics in self.config_data.get('指标', {}).values():
                if isinstance(category_metrics, list):
                    all_metrics.extend(category_metrics)

            if len(all_metrics) >= 2:
                metric1, metric2 = random.sample(all_metrics, 2)
            else:
                metric1 = self._get_metric_value()
                metric2 = self._get_metric_value()
                while metric1 == metric2:
                    metric2 = self._get_metric_value()

            question = template.format(dimension=dimension, metric1=metric1, metric2=metric2)
            questions.append(question)

        return questions

    def generate_trend_time_range_query(self, count: int = 5) -> List[str]:
        """生成趋势时间范围查询问题"""
        questions = []
        templates = self.question_templates['trend_time_range']

        for _ in range(count):
            template = self._get_random_item(templates)
            dimension = self._get_dimension_value(random.choice(list(self.config_data.get('维度', {}).keys())))
            metric = self._get_metric_value()
            time_range = self._get_time_value("time_range")

            question = template.format(dimension=dimension, metric=metric, time_range=time_range)
            questions.append(question)

        return questions

    def generate_trend_time_comparison_query(self, count: int = 5) -> List[str]:
        """生成趋势时间对比查询问题"""
        questions = []
        templates = self.question_templates['trend_time_comparison']

        for _ in range(count):
            template = self._get_random_item(templates)
            dimension = self._get_dimension_value(random.choice(list(self.config_data.get('维度', {}).keys())))
            metric = self._get_metric_value()
            time1 = self._get_time_value("time_point")
            time2 = self._get_time_value("time_point")

            # 确保两个时间不同
            while time1 == time2:
                time2 = self._get_time_value("time_point")

            question = template.format(dimension=dimension, metric=metric, time1=time1, time2=time2)
            questions.append(question)

        return questions

    def generate_single_drill_down_query(self, count: int = 5) -> List[str]:
        """生成单层下钻查询问题"""
        questions = []
        templates = self.question_templates['single_drill_down']

        # 定义层级关系
        drill_down_relations = {
            '中心': '群组',
            '群组': '业务',
            '省份': '城市',
        }

        for _ in range(count):
            template = self._get_random_item(templates)

            # 选择一个有下钻关系的维度
            parent_key = random.choice(list(drill_down_relations.keys()))
            child_key = drill_down_relations[parent_key]

            parent_dimension = self._get_dimension_value(parent_key)
            child_dimension = child_key  # 使用维度名称而不是具体值
            metric = self._get_metric_value()

            question = template.format(
                parent_dimension=parent_dimension,
                child_dimension=child_dimension,
                metric=metric
            )
            questions.append(question)

        return questions

    def generate_multi_drill_down_query(self, count: int = 5) -> List[str]:
        """生成多层下钻查询问题"""
        questions = []
        templates = self.question_templates['multi_drill_down']

        # 定义多层级关系
        multi_drill_relations = [
            ('中心', '群组', '业务'),
        ]

        for _ in range(count):
            template = self._get_random_item(templates)

            # 选择一个多层关系
            parent_key, child1_key, child2_key = random.choice(multi_drill_relations)

            parent_dimension = self._get_dimension_value(parent_key)
            child_dimension1 = child1_key  # 使用维度名称
            child_dimension2 = child2_key  # 使用维度名称
            metric = self._get_metric_value()

            question = template.format(
                parent_dimension=parent_dimension,
                child_dimension1=child_dimension1,
                child_dimension2=child_dimension2,
                metric=metric
            )
            questions.append(question)

        return questions

    def generate_all_question_types(self, count_per_type: int = 3) -> Dict[str, List[str]]:
        """生成所有类型的测试问题"""
        all_questions = {
            '指标达成-无时间查询': self.generate_no_time_query(count_per_type),
            '指标达成-单时间点查询': self.generate_single_time_query(count_per_type),
            '指标达成-时间范围查询': self.generate_time_range_query(count_per_type),
            '指标达成-时间对比查询': self.generate_time_comparison_query(count_per_type),
            '指标达成-维度对比查询': self.generate_dimension_comparison_query(count_per_type),
            '指标达成-维度计算聚合': self.generate_dimension_aggregation_query(count_per_type),
            '指标达成-时间计算聚合': self.generate_time_aggregation_query(count_per_type),
            '指标达成-多指标查询': self.generate_multi_metric_query(count_per_type),
            '趋势查询-时间范围查询': self.generate_trend_time_range_query(count_per_type),
            '趋势查询-时间对比查询': self.generate_trend_time_comparison_query(count_per_type),
            '下钻查询-单层下钻': self.generate_single_drill_down_query(count_per_type),
            '下钻查询-多层下钻': self.generate_multi_drill_down_query(count_per_type)
        }

        return all_questions

    def generate_questions_to_file(self, output_path: str, count_per_type: int = 5) -> None:
        """生成问题并保存到文件"""
        all_questions = self.generate_all_question_types(count_per_type)

        with open(output_path, 'w', encoding='utf-8') as f:
            f.write("# 自动生成的测试问题\n\n")

            for question_type, questions in all_questions.items():
                f.write(f"## {question_type}\n\n")
                for i, question in enumerate(questions, 1):
                    f.write(f"{i}. {question}\n")
                f.write("\n")

        self.logger.info(f"问题已生成并保存到: {output_path}")

    def save_questions_to_file(self, questions_by_type: Dict[str, List[str]], output_path: str) -> None:
        """
        保存已有的问题字典到文件
        
        Args:
            questions_by_type: 按类型分组的问题字典
            output_path: 输出文件路径
        """
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write("# 自动生成的测试问题\n\n")

            for question_type, questions in questions_by_type.items():
                f.write(f"## {question_type}\n\n")
                for i, question in enumerate(questions, 1):
                    f.write(f"{i}. {question}\n")
                f.write("\n")

        self.logger.info(f"问题已保存到: {output_path}")
