import os
import logging
import yaml
import random
import time
import base64
import json
import asyncio
import requests
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor
from utils.config_loader import config
from utils.auth import get_token
import aiohttp
import json

class AutoTester:
    """
    自动化测试工具，用于自动执行测试问题并截图
    """
    def __init__(self, config_dict=None):
        """
        初始化自动测试工具
        
        Args:
            config_dict (dict, optional): 配置字典，如果为None则使用全局配置
        """
        # 使用传入的配置或全局配置
        self.config = config_dict if config_dict else config
        
        # 配置日志
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        # 初始化飞书表格参数
        feishu_config = self.config.get('feishu', {})
        self.app_id = feishu_config.get('app_id')
        self.app_secret = feishu_config.get('app_secret')
        self.spreadsheet_token = feishu_config.get('spreadsheet_token')
        self.user_access_token = feishu_config.get('user_access_token')
        self.output_col = feishu_config.get('output_col', 'D')
        self.pic_col = feishu_config.get('pic_col', 'F')
        
        # 初始化LLM参数
        llm_config = self.config.get('llm', {})
        self.llm_api_key = llm_config.get('api_key')
        self.llm_api_url = llm_config.get('base_url')
        self.llm_model_name = llm_config.get('model_name')
        
        # 创建截图保存目录
        self.screenshot_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'screenshots')
        os.makedirs(self.screenshot_dir, exist_ok=True)
        self.logger.info(f"截图将保存至: {self.screenshot_dir}")
        
        # 每次启动时刷新token
        self.logger.info("应用启动，自动刷新用户访问令牌...")
        self._refresh_user_access_token()

    def _refresh_user_access_token(self):
        """刷新用户访问令牌并更新配置文件"""
        try:
            self.logger.info("正在获取新的用户访问令牌...")
            new_token = get_token(self.app_id, self.app_secret)
            
            if new_token:
                # 更新内存中的token
                self.user_access_token = new_token
                self.logger.info("用户访问令牌已刷新")
                
                # 更新配置文件
                self._update_config_file(new_token)
                return True
            else:
                self.logger.error("获取新的用户访问令牌失败")
                return False
        except Exception as e:
            self.logger.error(f"刷新用户访问令牌失败: {str(e)}")
            return False

    def _update_config_file(self, new_token):
        """更新配置文件中的user_access_token"""
        try:
            # 获取配置文件路径
            project_root = Path(__file__).parent.parent
            config_file = project_root / 'config' / 'config.yaml'
            
            if not config_file.exists():
                self.logger.error(f"配置文件不存在: {config_file}")
                return False
            
            # 读取当前配置文件
            with open(config_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 替换user_access_token
            import re
            pattern = r'(\s*user_access_token:\s*)[^\n]*'
            replacement = f'\\1{new_token}'
            
            new_content = re.sub(pattern, replacement, content)
            
            # 写入新配置
            with open(config_file, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            self.logger.info(f"配置文件已更新: {config_file}")
            return True
        except Exception as e:
            self.logger.error(f"更新配置文件失败: {str(e)}")
            return False

    async def initialize_login(self):
        """
        初始化登录，使用mcp-browser-sandbox完成登录流程
        """
        self.mcp_url = self.config.get('tars_agent', {}).get('url')
        if not self.mcp_url:
            raise ValueError('配置中缺少 tars_agent.url 字段')

        auth_state_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'auth_state.json')
        
        if os.path.exists(auth_state_path):
            self.logger.info("发现已保存的登录状态，将复用登录信息")
            with open(auth_state_path, 'r') as f:
                self.session_id = json.load(f).get('session_id')
            return
            
        self.logger.info("首次运行，需要登录...")
        
        # 创建mcp会话
        async with aiohttp.ClientSession() as session:
            # 创建浏览器会话
            async with session.post(f'{self.mcp_url}/create_session', json={
                'allow_url_list': ['https://cqc-tool-c-bot.gf-boe.bytedance.net/*']
            }) as response:
                result = await response.json()
                self.session_id = result.get('session_id')
                if not self.session_id:
                    raise ValueError('创建mcp会话失败')

            # 导航到登录页面
            await session.post(f'{self.mcp_url}/browser_navigate', json={
                'session_id': self.session_id,
                'url': 'https://cqc-tool-c-bot.gf-boe.bytedance.net/login'
            })

            self.logger.info("请在浏览器中完成登录...")
            # 等待登录完成（这里需要用户手动完成登录）
            await asyncio.sleep(120)  # 给用户2分钟时间完成登录

            # 验证是否登录成功
            async with session.post(f'{self.mcp_url}/browser_evaluate', json={
                'session_id': self.session_id,
                'script': 'window.location.href'
            }) as response:
                result = await response.json()
                current_url = result.get('result')
                if 'home' not in current_url:
                    raise ValueError('登录失败，请重试')

            # 保存会话ID作为登录状态
            with open(auth_state_path, 'w') as f:
                json.dump({'session_id': self.session_id}, f)
            self.logger.info("✅ 登录状态已保存，后续运行将自动登录")

    async def run_tests_for_questions(self, questions_with_rows):
        """
        运行问题测试并截图
        
        Args:
            questions_with_rows (list): 包含(行号,问题)元组的列表
        """
        # 初始化登录
        await self.initialize_login()
        
        self.logger.info(f"开始测试 {len(questions_with_rows)} 个问题...")
        
        # 获取最大并发线程数
        max_workers = self.config.get('feishu', {}).get('max_concurrent_threads', 2)
        self.logger.info(f"使用线程池，最大并发数: {max_workers}")
        
        # 创建任务列表
        tasks = []
        for idx, (row, question) in enumerate(questions_with_rows, start=1):
            task = asyncio.create_task(self.process_question(idx, row, question))
            tasks.append(task)
            await asyncio.sleep(2)  # 稍微延迟避免同时启动过多任务
        
        # 等待所有任务完成
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理结果
        for result in results:
            if isinstance(result, Exception):
                self.logger.error(f"处理问题时发生错误: {str(result)}")
                
        self.logger.info("所有问题处理完成")

    async def process_question(self, idx, row, question):
        """处理单个问题：截图并上传结果"""
        try:
            # 使用mcp-browser-sandbox进行测试
            self.mcp_url = self.config.get('tars_agent', {}).get('url')
            if not self.mcp_url:
                raise ValueError('配置中缺少 tars_agent.url 字段')

            # 加载保存的会话ID
            auth_state_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'auth_state.json')
            if not os.path.exists(auth_state_path):
                raise ValueError('未找到登录状态，请先运行initialize_login')
            with open(auth_state_path, 'r') as f:
                self.session_id = json.load(f).get('session_id')
                if not self.session_id:
                    raise ValueError('会话ID不存在，请重新登录')

            # 导航到提问页面
            async with aiohttp.ClientSession() as session:
                await session.post(f'{self.mcp_url}/browser_navigate', json={
                    'session_id': self.session_id,
                    'url': 'https://cqc-tool-c-bot.gf-boe.bytedance.net/home?agentId=cqcDataAssistant'
                })
                
                # 输入问题
                # 清空输入框并输入问题
                input_script = f"""
                const textarea = document.querySelector('textarea');
                textarea.value = {json.dumps(question)};
                textarea.dispatchEvent(new KeyboardEvent('keypress', {{ key: 'Enter', code: 'Enter' }}));
                """
                await session.post(f'{self.mcp_url}/browser_evaluate', json={{
                    'session_id': self.session_id,
                    'script': input_script
                }})

                # 等待回答完成
                self.logger.info(f"问题已提交: {question[:30]}... 等待回答完成")
                vision_config = self.config.get('feishu', {}).get('vision_detection', {{}})
                max_wait_time = vision_config.get('max_wait_time', 60)
                target_text = vision_config.get('target_text', '重新生成')

                await session.post(f'{self.mcp_url}/browser_wait_for', json={{
                    'session_id': self.session_id,
                    'text': target_text,
                    'time': max_wait_time
                }})

                # 截图
                safe_question = "".join([c for c in question if c.isalnum() or c in ' _-']).strip()[:50]
                screenshot_path = os.path.join(self.screenshot_dir, f'{idx}_{row}_{safe_question}.png')

                async with session.post(f'{self.mcp_url}/browser_take_screenshot', json={{
                    'session_id': self.session_id
                }}) as response:
                    result = await response.json()
                    base64_image = result.get('screenshot')
                    if not base64_image:
                        raise ValueError('获取截图失败')

                    with open(screenshot_path, 'wb') as f:
                        f.write(base64.b64decode(base64_image))

                self.logger.info(f"截图成功保存至: {screenshot_path}")

                # 随机刷新token (10%概率)
                if random.random() < 0.1:
                    self.logger.info("随机触发token刷新...")
                    self._refresh_user_access_token()

                # 上传图片到飞书并更新表格
                try:
                    self.upload_image_and_update_sheet(screenshot_path, row)
                    self.logger.info(f"已完成问题 {idx}: {question[:30]}... 截图保存至: {screenshot_path}")
                except Exception as e:
                    self.logger.error(f"上传图片或更新表格失败: {str(e)}")
                
        except Exception as e:
            self.logger.error(f"处理问题时出错: {str(e)}")
            import traceback
            self.logger.error(traceback.format_exc())

    async def wait_for_answer_and_screenshot(self, page, question):
        """等待回答完成并截图"""
        try:
            # 生成安全的截图文件名
            safe_question = "".join([c for c in question if c.isalnum() or c in ' _-']).strip()
            safe_question = safe_question[:50]  # 限制文件名长度
            screenshot_path = os.path.join(self.screenshot_dir, f'{safe_question}.png')
            
            # 从配置获取等待参数
            vision_config = self.config.get('feishu', {}).get('vision_detection', {})
            max_wait_time = vision_config.get('max_wait_time', 60)
            check_interval = vision_config.get('check_interval', 5)
            target_text = vision_config.get('target_text', "重新生成")
            max_retries = vision_config.get('max_retries', 3)
            
            retry_count = 0
            
            while retry_count <= max_retries:
                if retry_count > 0:
                    self.logger.info(f"🔄 第 {retry_count} 次重试")
                    
                elapsed_time = 0
                self.logger.info(f"开始等待回答完成，检测目标文本：'{target_text}'")
                
                while elapsed_time < max_wait_time:
                    # 等待一段时间
                    await asyncio.sleep(check_interval)
                    elapsed_time += check_interval
                    
                    # 检查是否有"重新生成"按钮出现，表示回答已完成
                    regenerate_button = page.get_by_text("重新生成", exact=True)
                    if await regenerate_button.count() > 0:
                        self.logger.info(f"✅ 检测到'重新生成'按钮！回答已完成，用时 {elapsed_time} 秒")
                        
                        # 等待一下让页面完全渲染
                        await asyncio.sleep(2)
                        
                        # 截取问答卡片
                        return await self.capture_qa_card(page, screenshot_path)
                    
                    # 检查是否有错误信息
                    error_text = page.get_by_text("执行错误，请重试", exact=True)
                    if await error_text.count() > 0:
                        self.logger.warning("❌ 检测到执行错误，准备重新输入问题")
                        
                        # 重新输入问题
                        await self.retry_input_question(page, question)
                        retry_count += 1
                        break  # 跳出内层循环，开始新的检测周期
                    
                    self.logger.info(f"等待中... ({elapsed_time}/{max_wait_time}秒)")
                else:
                    # 如果是因为超时退出的while循环
                    if retry_count < max_retries:
                        self.logger.warning(f"⚠️ 等待超时，但未达到最大重试次数，继续重试")
                        retry_count += 1
                    else:
                        self.logger.warning(f"⚠️ 达到最大重试次数，停止重试")
                        break
            
            # 最终截图
            await page.screenshot(path=screenshot_path)
            return screenshot_path
            
        except Exception as e:
            self.logger.error(f"等待和截图过程出错: {str(e)}")
            import traceback
            self.logger.error(traceback.format_exc())
            return None

    async def retry_input_question(self, page, question):
        """重新输入问题"""
        try:
            self.logger.info(f"🔄 重新输入问题: {question[:30]}...")
            
            # 等待一下让页面稳定
            await asyncio.sleep(2)
            
            # 查找输入框并重新输入
            input_box = page.get_by_role("textbox")
            await input_box.fill("")  # 清空输入框
            await asyncio.sleep(1)
            await input_box.fill(question)  # 重新输入问题
            await asyncio.sleep(1)
            await input_box.press("Enter")  # 提交
            
            self.logger.info("✅ 问题重新输入完成")
            await asyncio.sleep(3)  # 等待提交处理
            
        except Exception as e:
            self.logger.error(f"❌ 重新输入问题失败: {str(e)}")

    async def capture_qa_card(self, page, screenshot_path):
        """截取问答卡片"""
        try:
            self.logger.info("截取问答卡片...")
            
            # 从配置获取截图边界参数
            screenshot_config = self.config.get('feishu', {}).get('screenshot_boundaries', {})
            
            # 如果配置了固定边界参数，使用固定边界截图
            if screenshot_config:
                viewport = await page.viewport_size()
                width = viewport['width']
                height = viewport['height']
                
                # 固定边界参数（像素值）
                left = screenshot_config.get('left', 0)
                top = screenshot_config.get('top', 0)
                right = screenshot_config.get('right', width)
                bottom = screenshot_config.get('bottom', height)
                
                # 计算截图区域
                clip_area = {
                    'x': left,
                    'y': top,
                    'width': min(right - left, width - left),
                    'height': min(bottom - top, height - top)
                }
                
                self.logger.info(f"截图区域: {clip_area}")
                await page.screenshot(path=screenshot_path, clip=clip_area)
            else:
                # 如果没有配置固定边界，尝试定位问答卡片元素
                try:
                    # 尝试定位问答卡片容器
                    qa_container = page.locator('div.ant-pro-page-container-children-content')
                    if await qa_container.count() > 0:
                        await qa_container.screenshot(path=screenshot_path)
                        self.logger.info("成功截取问答卡片元素")
                    else:
                        # 如果找不到元素，截取整个页面
                        self.logger.warning("未找到问答卡片元素，截取整个页面")
                        await page.screenshot(path=screenshot_path)
                except Exception as e:
                    self.logger.warning(f"定位问答卡片元素失败: {str(e)}")
                    await page.screenshot(path=screenshot_path)
            
            return screenshot_path
            
        except Exception as e:
            self.logger.error(f"截图失败: {str(e)}")
            # 回退到整页截图
            try:
                await page.screenshot(path=screenshot_path)
                return screenshot_path
            except:
                return None

    def upload_image_and_update_sheet(self, image_path, row, max_try=3):
        """上传图片到飞书表格"""
        attempt = 0
        while attempt < max_try:
            try:
                self.logger.info(f"正在插入图片到表格第{row}行: {os.path.basename(image_path)}")
                
                # 随机刷新token (10%概率)
                if random.random() < 0.1:
                    self.logger.info("随机触发token刷新...")
                    self._refresh_user_access_token()
                
                # 读取图片并转换为base64
                with open(image_path, "rb") as f:
                    image_content = f.read()
                    image_base64 = base64.b64encode(image_content).decode('utf-8')
                
                # 构建API URL和数据
                spreadsheet_token_val, sheet_id = self.spreadsheet_token.strip().split('_')
                position = f"{self.pic_col}{row}"
                
                url = f"https://open.feishu.cn/open-apis/sheets/v2/spreadsheets/{spreadsheet_token_val}/values_image"
                
                data = {
                    "range": f"{sheet_id}!{position}",
                    "image": image_base64,
                    "name": os.path.basename(image_path)
                }
                
                headers = {
                    "Authorization": f"Bearer {self.user_access_token}",
                    "Content-Type": "application/json"
                }
                
                # 发送请求
                response = requests.post(url, json=data, headers=headers)
                
                self.logger.info(f"图片插入状态码: {response.status_code}")
                result = response.json()
                
                if response.status_code == 200 and result.get('code') == 0:
                    self.logger.info(f"✅ 图片成功插入到表格第{row}行: {position}")
                    return True
                else:
                    self.logger.warning(f"❌ 图片插入失败: {result}")
                    # 如果是token过期，尝试刷新token
                    if result.get('code') == 99991663:
                        self.logger.info("Token可能已过期，尝试刷新...")
                        self._refresh_user_access_token()
                    
                    attempt += 1
                    time.sleep(2)
            except Exception as e:
                self.logger.error(f"❌ 图片插入异常: {str(e)}")
                import traceback
                self.logger.error(traceback.format_exc())
                attempt += 1
                time.sleep(2)
        
        self.logger.error(f"❌ 图片插入失败，已达到最大尝试次数: {max_try}")
        return False
